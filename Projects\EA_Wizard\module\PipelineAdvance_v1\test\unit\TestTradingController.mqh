//+------------------------------------------------------------------+
//|                                         TestTradingController.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../TradingController.mqh"
#include "TestTradingPipeline.mqh"  // 使用 MockTradingPipeline

//+------------------------------------------------------------------+
//| Mock TradingPipelineContainerManager 類                         |
//| 用於模擬容器管理器的行為以便進行單元測試                         |
//+------------------------------------------------------------------+
class MockTradingPipelineContainerManager
{
private:
    bool m_isExecuted;
    bool m_shouldExecuteSucceed;
    string m_name;

public:
    // 構造函數
    MockTradingPipelineContainerManager(string name = "MockManager", bool shouldSucceed = true)
        : m_name(name), m_shouldExecuteSucceed(shouldSucceed), m_isExecuted(false) {}

    // 析構函數
    virtual ~MockTradingPipelineContainerManager() {}

    // 模擬執行方法
    void Execute(ENUM_TRADING_EVENT event)
    {
        m_isExecuted = m_shouldExecuteSucceed;
    }

    // 模擬檢查執行狀態
    bool IsExecuted() const { return m_isExecuted; }

    // 設置執行結果
    void SetShouldExecuteSucceed(bool should) { m_shouldExecuteSucceed = should; }

    // 重置狀態
    void Reset() { m_isExecuted = false; }

    // 獲取名稱
    string GetName() const { return m_name; }
};

//+------------------------------------------------------------------+
//| TradingController 單元測試類                                    |
//+------------------------------------------------------------------+
class TestTradingController : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingController(TestRunner* runner = NULL)
        : TestCase("TestTradingController"), m_runner(runner) {}

    // 析構函數
    virtual ~TestTradingController() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 TradingController 單元測試 ===");

        // 在開始所有測試前先重置驅動器狀態
        Print("=== 初始化測試環境 ===");
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        if(driver != NULL)
        {
            Print("  重置 TradingPipelineDriver 全局狀態...");
            driver.Reset();
            Print("  TradingPipelineDriver 重置完成");
        }

        TestConstructor();
        TestBasicProperties();
        TestOnInitFlow();
        TestOnTickFlow();
        TestOnDeinitFlow();
        TestDriverIntegration();

        Print("=== TradingController 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 TradingController 構造函數 ---");

        // 測試默認構造函數
        TradingController* controller1 = new TradingController();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestConstructor - 默認構造函數",
                controller1 != NULL,
                controller1 != NULL ? "構造函數成功" : "構造函數失敗"
            ));

            // 測試默認名稱
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestConstructor - 默認名稱",
                controller1.GetName() == TRADING_CONTROLLER_NAME,
                "默認名稱設置正確"
            ));

            // 測試默認類型
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestConstructor - 默認類型",
                controller1.GetType() == TRADING_CONTROLLER_TYPE,
                "默認類型設置正確"
            ));
        }

        // 測試帶參數的構造函數
        TradingController* controller2 = new TradingController("CustomController");

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestConstructor - 帶參數構造函數",
                controller2 != NULL && controller2.GetName() == "CustomController",
                "帶參數構造函數成功，名稱正確"
            ));
        }

        delete controller1;
        delete controller2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 TradingController 基本屬性 ---");

        TradingController* controller = new TradingController("PropertyTest");

        if(m_runner != NULL)
        {
            // 測試初始化狀態（應該為 false）
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestBasicProperties - 初始化狀態",
                !controller.IsInitialized(),
                "初始化狀態正確（未初始化）"
            ));

            // 測試名稱獲取
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestBasicProperties - 名稱獲取",
                controller.GetName() == "PropertyTest",
                "名稱獲取正確"
            ));

            // 測試類型獲取
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestBasicProperties - 類型獲取",
                controller.GetType() == TRADING_CONTROLLER_TYPE,
                "類型獲取正確"
            ));

            // 測試結果獲取（不應該為 NULL）
            PipelineResult* result = controller.GetResult();
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestBasicProperties - 結果不為空",
                result != NULL,
                result != NULL ? "結果對象存在" : "結果對象為空"
            ));

            // 測試驅動器獲取
            TradingPipelineDriver* driver = controller.GetDriver();
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestBasicProperties - 驅動器獲取",
                driver != NULL,
                driver != NULL ? "驅動器獲取成功" : "驅動器獲取失敗"
            ));
        }

        delete controller;
    }

    // 測試 OnInit 流程
    void TestOnInitFlow()
    {
        Print("--- 測試 TradingController OnInit 流程 ---");

        // 先重置驅動器狀態以確保測試環境乾淨
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        if(driver != NULL)
        {
            Print("  重置 TradingPipelineDriver 狀態...");
            driver.Reset();
        }

        TradingController* controller = new TradingController("InitTest");

        if(m_runner != NULL)
        {
            // 測試 OnInit 執行
            ENUM_INIT_RETCODE initResult = controller.OnInit();

            // 注意：由於依賴真實的 TradingPipelineDriver，結果可能因環境而異
            // 這裡主要測試方法能正常執行而不崩潰
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestOnInitFlow - OnInit 執行",
                true,  // 只要能執行到這裡就算成功
                "OnInit 方法執行完成"
            ));

            // 測試初始化後的狀態
            bool isInitialized = controller.IsInitialized();
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestOnInitFlow - 初始化狀態更新",
                true,  // 無論成功失敗，狀態都應該被設置
                StringFormat("初始化狀態: %s", isInitialized ? "已初始化" : "未初始化")
            ));

            // 測試結果更新
            PipelineResult* result = controller.GetResult();
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestOnInitFlow - 結果更新",
                result != NULL,
                "OnInit 後結果已更新"
            ));
        }

        delete controller;
    }

    // 測試 OnTick 流程
    void TestOnTickFlow()
    {
        Print("--- 測試 TradingController OnTick 流程 ---");

        // 先重置驅動器狀態以確保測試環境乾淨
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        if(driver != NULL)
        {
            Print("  重置 TradingPipelineDriver 狀態...");
            driver.Reset();
        }

        TradingController* controller = new TradingController("TickTest");

        if(m_runner != NULL)
        {
            // 測試未初始化時的 OnTick
            controller.OnTick();
            PipelineResult* result1 = controller.GetResult();
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestOnTickFlow - 未初始化時 OnTick",
                result1 != NULL && !result1.IsSuccess(),
                "未初始化時 OnTick 正確處理"
            ));

            // 嘗試初始化
            controller.OnInit();

            // 測試初始化後的 OnTick
            controller.OnTick();
            PipelineResult* result2 = controller.GetResult();
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestOnTickFlow - 初始化後 OnTick",
                result2 != NULL,
                "初始化後 OnTick 執行完成"
            ));
        }

        delete controller;
    }

    // 測試 OnDeinit 流程
    void TestOnDeinitFlow()
    {
        Print("--- 測試 TradingController OnDeinit 流程 ---");

        // 先重置驅動器狀態以確保測試環境乾淨
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        if(driver != NULL)
        {
            Print("  重置 TradingPipelineDriver 狀態...");
            driver.Reset();
        }

        TradingController* controller = new TradingController("DeinitTest");

        if(m_runner != NULL)
        {
            // 先初始化
            controller.OnInit();

            // 測試 OnDeinit
            controller.OnDeinit(REASON_REMOVE);

            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestOnDeinitFlow - OnDeinit 執行",
                true,
                "OnDeinit 方法執行完成"
            ));

            // 測試清理後的狀態
            bool isInitialized = controller.IsInitialized();
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestOnDeinitFlow - 清理後狀態",
                !isInitialized,
                "清理後初始化狀態重置"
            ));

            // 測試結果更新
            PipelineResult* result = controller.GetResult();
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestOnDeinitFlow - 結果更新",
                result != NULL,
                "OnDeinit 後結果已更新"
            ));
        }

        delete controller;
    }

    // 測試驅動器整合
    void TestDriverIntegration()
    {
        Print("--- 測試 TradingController 驅動器整合 ---");

        // 先重置驅動器狀態以確保測試環境乾淨
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        if(driver != NULL)
        {
            Print("  重置 TradingPipelineDriver 狀態...");
            driver.Reset();
        }

        TradingController* controller = new TradingController("DriverTest");

        if(m_runner != NULL)
        {
            // 測試驅動器獲取
            TradingPipelineDriver* driver = controller.GetDriver();
            m_runner.RecordResult(new TestResult(
                "TestTradingController::TestDriverIntegration - 驅動器不為空",
                driver != NULL,
                driver != NULL ? "驅動器獲取成功" : "驅動器獲取失敗"
            ));

            if(driver != NULL)
            {
                // 測試驅動器是否為單例
                TradingPipelineDriver* driver2 = TradingPipelineDriver::GetInstance();
                m_runner.RecordResult(new TestResult(
                    "TestTradingController::TestDriverIntegration - 單例一致性",
                    driver == driver2,
                    "驅動器單例一致性正確"
                ));
            }
        }

        delete controller;
    }
};
